import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { MobileOrdersService } from './services/mobile-orders.service';
import { OrderStatus } from './domain/order';
import { UpdateOrderStatusDto } from './dto/update-order-status.dto';
import { OrderDetailsResponseDto } from './dto/order-details-response.dto';
import { MobileOrderListItemDto } from '@app/business/mobile/orders/dto/order-list-response.dto';
import { formatDateTime } from '../../pricing/utils/date.utils';
import { DEFAULT_DATETIME_FORMAT_12H } from '../../pricing/constants/timezone.constants';

@ApiTags('Mobile - Orders')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller({
  path: 'mobile/orders',
  version: '1',
})
export class MobileOrdersController {
  constructor(private readonly ordersService: MobileOrdersService) {}

  @Get()
  @ApiOperation({ summary: 'Get orders for the authenticated driver' })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: OrderStatus,
    isArray: true,
    description: 'Filter orders by status',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of orders retrieved successfully',
    type: [MobileOrderListItemDto],
  })
  async getDriverOrders(
    @Request() req: any,
    @Query('status') status?: OrderStatus | OrderStatus[],
  ) {
    const driverId = req.user.sub;
    const orders = await this.ordersService.getDriverOrders(driverId, status);

    // Transform orders into the format shown in the screenshot with pickup and delivery pairs
    const formattedOrders: MobileOrderListItemDto[] = [];

    orders.forEach((order) => {
      // Determine status display text based on order status
      const statusText =
        this.ordersService.getStatusText(order.status, order) || 'Submitted';

      // Remove TRK- prefix if present, otherwise just use the tracking number
      const orderNumberDisplay =
        order.trackingNumber?.replace(/^(ORD-|TRK-)/, '') || '';

      // Create display time string for delivery (or use current time if null)
      const scheduledCollectionTime = formatDateTime(
        order.scheduledCollectionTime,
        DEFAULT_DATETIME_FORMAT_12H,
      );
      const scheduledDeliveryTime = formatDateTime(
        order.scheduledDeliveryTime,
        DEFAULT_DATETIME_FORMAT_12H,
      );

      // Get customer name
      const customerName = order.customerName || 'Unknown Customer';
      const orderType = this.ordersService.determineOrderType(order.status);
      // Format based on the screenshot
      formattedOrders.push(<MobileOrderListItemDto>{
        id: order.id,
        orderNumber: orderNumberDisplay,
        customerName: customerName,
        companyName: order.companyName,
        type: orderType as string,
        scheduledCollectionTime: scheduledCollectionTime,
        scheduledDeliveryTime: scheduledDeliveryTime,
        status: statusText,
        collectionLocation: order.collectionAddress,
        deliveryLocation: order.deliveryAddress,
        requestedBy: order.requestedByName || 'Unknown',
        recipientName: order.deliveryContactName,
        serviceLevel: order.serviceLevel,
        description: order.description,
        totalPrice: order.totalPrice,
        quantity: order.totalItems,
        weight: order.totalWeight,
        dimensions: this.calculateDimensions(order.items),
        notes: order.internalNotes,
      });
    });
    return formattedOrders;
  }

  @Get('unassigned')
  @ApiOperation({ summary: 'Get all unassigned orders for the tenant' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of unassigned orders retrieved successfully',
  })
  async getUnassignedOrders(@Req() req: any) {
    const unassignedOrders = await this.ordersService.getUnassignedOrders(
      req.user.sub,
    );
    const formattedOrders: MobileOrderListItemDto[] = [];

    unassignedOrders.forEach((order) => {
      // Determine status display text based on order status
      const statusText =
        this.ordersService.getStatusText(order.status, order) || 'Submitted';

      // Remove TRK- prefix if present, otherwise just use the tracking number
      const orderNumberDisplay =
        order.trackingNumber?.replace(/^(ORD-|TRK-)/, '') || '';

      // Create display time string for delivery (or use current time if null)
      const scheduledCollectionTime = formatDateTime(
        order.scheduledCollectionTime,
        DEFAULT_DATETIME_FORMAT_12H,
      );
      const scheduledDeliveryTime = formatDateTime(
        order.scheduledDeliveryTime,
        DEFAULT_DATETIME_FORMAT_12H,
      );

      // Format based on the screenshot
      formattedOrders.push(<any>{
        id: order.id,
        orderNumber: orderNumberDisplay,
        customerName: order.customerName || 'Unknown Customer',
        companyName: order.companyName,
        scheduledCollectionTime: scheduledCollectionTime,
        scheduledDeliveryTime: scheduledDeliveryTime,
        status: statusText,
        collectionLocation: order.collectionAddress,
        deliveryLocation: order.deliveryAddress,
        requestedBy: order.requestedByName || 'Unknown',
        recipientName: order.deliveryContactName,
        serviceLevel: order.serviceLevel,
        description: order.description,
        internalNotes: order.internalNotes,
        totalPrice: Number((+order.totalPrice || 0).toFixed(2)),
        quantity: Number(order.totalItems) || 0,
        weight: Number((+order.totalWeight || 0).toFixed(2)),
        dimensions: this.calculateDimensions(order.items),
      });
    });

    return formattedOrders;
  }

  @Get('unassigned/:id')
  @ApiOperation({ summary: 'Get unassigned order details by ID' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Unassigned order details retrieved successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  async getUnassignedOrderDetails(
    @Request() req: any,
    @Param('id') id: string,
  ) {
    const driverId = req.user.sub;
    const order = await this.ordersService.getOrderById(id, driverId, true);

    // Map status from enum to user-friendly text
    const statusText = this.ordersService.getStatusText(order.status, order);

    // Remove TRK- prefix if present, otherwise just use the tracking number
    const orderNumberDisplay =
      order.trackingNumber?.replace(/^(ORD-|TRK-)/, '') || '';

    // Format order details according to the UI
    const response: OrderDetailsResponseDto = {
      id: order.id,
      orderNumber: orderNumberDisplay,
      status: statusText,
      companyName: order.companyName,
      customerName: order.customerName || 'Unknown Customer',
      requestedBy: order.requestedByName || order.customerName || 'Unknown',
      collectionTime: formatDateTime(
        order.scheduledCollectionTime,
        DEFAULT_DATETIME_FORMAT_12H,
      ),
      deliveryTime: formatDateTime(
        order.scheduledDeliveryTime,
        DEFAULT_DATETIME_FORMAT_12H,
      ),
      collectionLocation: order.collectionAddress,
      deliveryLocation: order.deliveryAddress,
      isCod: order.isCod,
      codAmount: order.codAmount,
      serviceLevel: order.serviceLevel,
      serviceOptions: this.extractServiceOptions(order),
      collectionSignatureRequired: order.collectionSignatureRequired,
      deliverySignatureRequired: order.deliverySignatureRequired,
      description: order.description,
      internalNotes: order.internalNotes,
      totalPrice: Number((+order.totalPrice || 0).toFixed(2)),
      quantity: Number(order.totalItems) || 0,
      weight: Number((+order.totalWeight || 0).toFixed(2)),
      dimensions: this.calculateDimensions(order.items),
      attachments: order.attachments,
    };

    return response;
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get order details by ID (Assigned order)' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order details retrieved successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({ description: 'Order not assigned to driver' })
  async getOrderDetails(@Request() req: any, @Param('id') id: string) {
    const driverId = req.user.sub;
    const order = await this.ordersService.getDriverOrderById(driverId, id);

    // Map status from enum to user-friendly text
    const statusText = this.ordersService.getStatusText(order.status, order);

    // Format order details according to the UI
    const response: OrderDetailsResponseDto = {
      id: order.id,
      orderNumber: order.trackingNumber?.replace('ORD-', '') || '',
      status: statusText,
      companyName: order.companyName,
      customerName: order.customerName || 'Unknown Customer',
      requestedBy: order.requestedByName || order.customerName || 'Unknown',
      collectionTime: formatDateTime(
        order.scheduledCollectionTime,
        DEFAULT_DATETIME_FORMAT_12H,
      ),
      deliveryTime: formatDateTime(
        order.scheduledDeliveryTime,
        DEFAULT_DATETIME_FORMAT_12H,
      ),
      collectionLocation: order.collectionAddress,
      deliveryLocation: order.deliveryAddress,
      isCod: order.isCod,
      codAmount: order.codAmount,
      serviceLevel: order.serviceLevel,
      serviceOptions: this.extractServiceOptions(order),
      collectionSignatureRequired: order.collectionSignatureRequired,
      deliverySignatureRequired: order.deliverySignatureRequired,
      description: order.description,
      internalNotes: order.internalNotes,
      totalPrice: Number((+order.totalPrice || 0).toFixed(2)),
      quantity: Number(order.totalItems) || 0,
      weight: Number((+order.totalWeight || 0).toFixed(2)),
      dimensions: this.calculateDimensions(order.items),
      attachments: order.attachments,
    };

    return response;
  }

  @Put(':id/status')
  @ApiOperation({ summary: 'Update order status' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiBody({ type: UpdateOrderStatusDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order status updated successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({ description: 'Order not assigned to driver' })
  @ApiBadRequestResponse({ description: 'Invalid status transition' })
  async updateOrderStatus(
    @Request() req: any,
    @Param('id') id: string,
    @Body() updateDto: UpdateOrderStatusDto,
  ) {
    const driverId = req.user.sub;
    await this.ordersService.updateOrderStatus(driverId, id, updateDto);
    return this.getOrderDetails(req, id); // Return the full order details
  }

  @Post(':id/accept')
  @ApiOperation({ summary: 'Accept an order' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order accepted successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiBadRequestResponse({ description: 'Order cannot be accepted' })
  async acceptOrder(@Request() req: any, @Param('id') id: string) {
    const driverId = req.user.sub;
    await this.ordersService.acceptOrder(driverId, id);
    return this.getOrderDetails(req, id); // Return the full order details
  }

  @Post(':id/reject')
  @ApiOperation({ summary: 'Reject an order' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        reason: { type: 'string', example: 'Vehicle issue' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order rejected successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({ description: 'Order not assigned to driver' })
  @ApiBadRequestResponse({ description: 'Order cannot be rejected' })
  async rejectOrder(
    @Request() req: any,
    @Param('id') id: string,
    @Body('reason') reason: string,
  ) {
    const driverId = req.user.sub;
    await this.ordersService.rejectOrder(driverId, id, reason);
    return this.getOrderDetails(req, id); // Return the full order details
  }

  @Post(':id/transfer')
  @ApiOperation({ summary: 'Transfer an order to another driver' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        newDriverId: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655440002',
        },
        reason: { type: 'string', example: 'Driver unavailable' },
      },
      required: ['newDriverId'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order transferred successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({ description: 'Order not assigned to driver' })
  @ApiBadRequestResponse({ description: 'Order cannot be transferred' })
  async transferOrder(
    @Request() req: any,
    @Param('id') id: string,
    @Body('newDriverId') newDriverId: string,
    @Body('reason') reason: string,
  ) {
    const driverId = req.user.sub;
    await this.ordersService.transferOrder(
      driverId,
      id,
      newDriverId,
      reason || 'Driver requested transfer',
    );
    return this.getOrderDetails(req, id); // Return the full order details
  }

  /**
   * Extract service options from order data
   * This replaces hardcoded service options with dynamic ones based on the order
   */
  private extractServiceOptions(order: any): { description: string }[] {
    const serviceOptions: { description: string }[] = [];
    // Get price modifiers from order metadata if available

    const pricingSummary = (order as any).pricingSummary;
    if (pricingSummary && pricingSummary.modifiers) {
      const serviceOptions = pricingSummary.modifiers.map((modifier: any) => ({
        description: modifier.name || modifier.description || 'Price modifier',
        amount: +modifier.amount || 0,
      }));

      serviceOptions.push({
        description: `basePrice`,
        amount: +pricingSummary.basePrice || 0,
      });
      return serviceOptions;
    }

    // If no price modifiers in metadata, extract from other order info
    // Check if there's a base price calculation
    if (order.basePrice) {
      // Convert to number if it's a string
      const basePrice =
        typeof order.basePrice === 'string'
          ? parseFloat(order.basePrice)
          : order.basePrice;

      serviceOptions.push({
        description: `Base price: ${Number(basePrice).toFixed(2)}`,
      });
    }

    // Check for misc adjustments
    if (order.miscAdjustment && order.miscAdjustment !== 0) {
      // Convert to number if it's a string
      const miscAdjustment =
        typeof order.miscAdjustment === 'string'
          ? parseFloat(order.miscAdjustment)
          : order.miscAdjustment;

      serviceOptions.push({
        description: `Adjustment: ${miscAdjustment > 0 ? '+' : ''}${Number(miscAdjustment).toFixed(2)}`,
      });
    }

    // If we still don't have any service options, provide minimal info
    if (serviceOptions.length === 0) {
      serviceOptions.push({
        description: 'Standard delivery service',
      });
    }

    return serviceOptions;
  }

  /**
   * Calculate dimensions based on order items
   */
  private calculateDimensions(items: any[] | undefined): string {
    if (!items || items.length === 0) {
      return 'N/A';
    }

    // Try to extract dimensions from items if they exist
    const itemsWithDimensions = items.filter(
      (item) => item.length && item.width && item.height,
    );

    if (itemsWithDimensions.length > 0) {
      // Use the largest item's dimensions as representative
      const largestItem = itemsWithDimensions.reduce((largest, item) => {
        const currentVolume = item.length * item.width * item.height;
        const largestVolume = largest.length * largest.width * largest.height;
        return currentVolume > largestVolume ? item : largest;
      }, itemsWithDimensions[0]);

      return `${largestItem.length} × ${largestItem.width} × ${largestItem.height} ${largestItem.dimensionUnit || ''}`;
    }

    // If no specific dimensions, return N/A
    return 'N/A';
  }

  /**
   * Determine the delivery type based on scheduled collection and delivery times
   * This replaces hardcoded delivery type logic with a consistent service-level function
   */
  private determineDeliveryType(
    scheduledCollectionTime: Date | string | undefined,
    scheduledDeliveryTime: Date | string | undefined,
    metadataServiceLevel?: string,
  ): string {
    // First check if there's an explicit service level in metadata or from the database
    if (metadataServiceLevel) {
      return metadataServiceLevel;
    }

    // If both times are provided, determine based on date comparison
    if (scheduledCollectionTime && scheduledDeliveryTime) {
      const pickupDate = new Date(scheduledCollectionTime);
      const deliveryDate = new Date(scheduledDeliveryTime);

      const isSameDay =
        pickupDate.getDate() === deliveryDate.getDate() &&
        pickupDate.getMonth() === deliveryDate.getMonth() &&
        pickupDate.getFullYear() === deliveryDate.getFullYear();

      // Calculate hours difference to determine service level
      const hoursDiff =
        (deliveryDate.getTime() - pickupDate.getTime()) / (1000 * 60 * 60);

      // Determine service level based on time difference
      if (isSameDay) {
        if (hoursDiff <= 2) return 'Express';
        if (hoursDiff <= 4) return 'Priority';
        return 'Sameday';
      } else {
        if (hoursDiff <= 24) return 'Overnight';
        if (hoursDiff <= 48) return 'Nextday';
        return 'Standard';
      }
    }

    // Default to Standard if we can't determine
    return 'Standard';
  }
}
